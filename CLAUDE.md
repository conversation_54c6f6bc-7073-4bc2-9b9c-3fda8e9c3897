# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an STM32F407-based dual-axis stepper motor control system with integrated Raspberry Pi vision feedback and PID control algorithms. The system controls X/Y axis independently and communicates with external devices through multiple UART interfaces.

## Development Environment

### Build System
- **IDE**: Keil MDK-ARM V5.32
- **Compiler**: ARM Compiler (defined in MDK-ARM project file)
- **Configuration**: STM32CubeMX generated project (ZDT.ioc)
- **Target**: STM32F407VETx microcontroller

### Build Commands
```bash
# Build project using Keil MDK-ARM
# Open ZDT.uvprojx in Keil µVision IDE and use:
# Project -> Build Target (F7)
# Project -> Rebuild all target files (F7)

# Flash to target
# Project -> Download (F8)
```

### Key Build Files
- `MDK-ARM/ZDT.uvprojx` - Main Keil project file
- `ZDT.ioc` - STM32CubeMX configuration file
- Build outputs in `MDK-ARM/ZDT/` directory

## Code Architecture

### Hardware Abstraction Layers
1. **HAL Layer**: STM32 HAL drivers in `Drivers/STM32F4xx_HAL_Driver/`
2. **BSP Layer**: Board Support Package in `bsp/` directory
3. **Application Layer**: Application logic in `app/` directory
4. **Core Layer**: STM32CubeMX generated code in `Core/`

### Key System Components

#### UART Communication System
- **USART1**: Debug interface (PA9/PA10, 115200 baud)
- **USART2**: X-axis stepper motor (PA2/PA3, 115200 baud, DMA)
- **USART3**: Y-axis stepper motor (PB10/PB11, 115200 baud, DMA) 
- **USART6**: Raspberry Pi communication (PC6/PC7, 115200 baud, DMA)
- **UART4/UART5**: Additional interfaces (currently disabled/unused)

#### DMA Configuration
- `DMA1_Stream5`: USART2_RX (X-axis motor feedback)
- `DMA1_Stream1`: USART3_RX (Y-axis motor feedback)
- `DMA2_Stream1`: USART6_RX (Raspberry Pi data)

#### Motor Control System
- **Protocol**: Emm_V5 stepper motor control protocol (`app/Emm_V5.c`)
- **Controllers**: Dual-axis independent control
- **Feedback**: Real-time position and velocity feedback
- **Control Modes**: Position control, velocity control, PWM control

#### Data Processing
- **Ring Buffers**: Circular buffer implementation (`ringbuffer/`) for UART data
- **PID Control**: Custom PID algorithm (`app/mypid.c`)
- **Encoder Support**: Quadrature encoder interface using TIM3/TIM4

### Critical System Configuration Notes

#### Recent Y-Axis Motor Fix (2025-01-29)
The Y-axis motor communication was switched from UART4 to USART3 due to hardware issues:
- `MOTOR_Y_UART` macro changed from `huart4` to `huart3` in `bsp/step_motor_bsp.h:10`
- Interrupt handling moved to `USART3_IRQHandler` in `Core/Src/stm32f4xx_it.c`
- DMA configuration updated accordingly

#### Interrupt-Driven Communication
All motor communication uses interrupt-driven DMA with automatic restart:
```c
// Pattern used in interrupt handlers
void USART3_IRQHandler(void) {
    HAL_UART_IRQHandler(&huart3);
    HAL_UARTEx_ReceiveToIdle_DMA(&huart3, motor_y_buf, sizeof(motor_y_buf));
    __HAL_DMA_DISABLE_IT(&hdma_usart3_rx, DMA_IT_HT);
}
```

### File Organization
- `Core/`: STM32CubeMX generated HAL initialization code
- `bsp/`: Hardware abstraction layer (GPIO, UART, motor interfaces)
- `app/`: Application-specific drivers (Emm_V5, PID, encoder processing)
- `ringbuffer/`: Circular buffer data structure implementation
- `OLED/`: Display driver (currently unused)
- `TB6612/`: Alternative motor driver (currently unused)

### Key APIs and Usage Patterns

#### Motor Control
```c
// Motor initialization
Step_Motor_Init();

// Speed control (RPM)
Step_Motor_Set_Speed_my(x_rpm, y_rpm);

// Position control using Emm_V5 protocol
Emm_V5_Pos_Control(&huart3, MOTOR_Y_ADDR, 0, distance, 0, speed, 0, 0);

// Emergency stop
Step_Motor_Stop();
```

#### Data Communication
```c
// Ring buffer operations
rt_ringbuffer_put(&ringbuffer_x, data, length);
rt_ringbuffer_get(&ringbuffer_x, buffer, length);

// UART transmission
HAL_UART_Transmit(&huart2, data, length, timeout);
```

## Common Development Tasks

### Adding New Motor Commands
1. Define command in `app/Emm_V5.h` 
2. Implement function in `app/Emm_V5.c`
3. Add BSP wrapper in `bsp/step_motor_bsp.c`
4. Update main control logic in `Core/Src/main.c`

### Modifying Communication Interfaces
1. Update pin assignments in `ZDT.ioc` using STM32CubeMX
2. Regenerate code and preserve user sections
3. Update DMA configurations in `Core/Src/dma.c`
4. Modify interrupt handlers in `Core/Src/stm32f4xx_it.c`

### PID Tuning
- Parameters defined in `app/mypid.c`
- Tuning interface through debug UART or direct code modification
- Real-time feedback available through encoder interfaces

## Testing and Debugging

### Debug Interface
- Primary debug output through USART1 (115200 baud)
- Use `printf` redirection for debugging output
- Real-time monitoring of motor positions and system status

### Hardware Testing
- Encoder feedback verification using TIM3/TIM4 interfaces
- Motor response testing through step commands
- Communication integrity checking via ring buffer status

## Safety Notes
- Motor emergency stop functionality implemented
- Position limits enforced in software (`MOTOR_MAX_ANGLE = 50°`)
- DMA timeout and error handling for communication reliability